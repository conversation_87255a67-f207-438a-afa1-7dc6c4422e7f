import Image from "next/image";
import React, { useEffect, useState, useCallback } from "react";
import Link from "next/link";
import CancellationModal from "../CancellationModal/page";
import ReactStars from "react-rating-stars-component";
import { useParams, usePathname } from "next/navigation";
import InfiniteScroll from "react-infinite-scroll-component";
export default function Booking({ playerData, appliedFilters }) {
  const params = useParams();
  const pathname = usePathname();

  const [bookings, setBookings] = useState([]);
  const [filteredBookings, setFilteredBookings] = useState([]);
  const [hasMore, setHasMore] = useState(true);
  const [pageNumber, setPageNumber] = useState(1);

  const [loading, setLoading] = useState(true);

  // Client-side filtering function
  const applyFilters = useCallback((bookingsData) => {
    if (appliedFilters.length === 0) {
      return bookingsData;
    }

    return bookingsData.filter((booking) => {
      // Group filters by type
      const courseTypeFilters = appliedFilters.filter(f => f.startsWith('courseType='));
      const statusFilters = appliedFilters.filter(f => f.startsWith('status='));

      let matchesCourseType = true;
      let matchesStatus = true;

      // Check courseType filters (OR logic within same type)
      if (courseTypeFilters.length > 0) {
        const courseTypes = courseTypeFilters.map(f => f.split('=')[1]);
        matchesCourseType = courseTypes.some(type => {
          if (type === 'course') {
            return booking?.courseId?.classType === 'course';
          } else if (type === 'class') {
            return booking?.courseId?.classType === 'class';
          }
          return false;
        });
      }

      // Check status filters (OR logic within same type)
      if (statusFilters.length > 0) {
        const statuses = statusFilters.map(f => f.split('=')[1]);
        matchesStatus = statuses.some(status => {
          return booking?.status === status;
        });
      }

      // AND logic between different filter types
      return matchesCourseType && matchesStatus;
    });
  }, [appliedFilters]);

  // Update filtered bookings when bookings or filters change
  useEffect(() => {
    const filtered = applyFilters(bookings);
    console.log('Applied filters:', appliedFilters);
    console.log('Total bookings:', bookings.length);
    console.log('Filtered bookings:', filtered.length);
    setFilteredBookings(filtered);
  }, [bookings, applyFilters]);
  const fetchBookings = useCallback(
    async (token, page, reset = false) => {
      try {
        // Fetch all bookings without filters first
        const requestOptions = {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          redirect: "follow",
        };
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_BASE_URL}/api/booking/?playerId=${playerData._id}&page=${page}`,
          requestOptions
        );
        const result = await response.json();

        if (result.data.length === 0) {
          setHasMore(false);
        } else {
          setBookings((prevBookings) =>
            reset ? result.data : [...prevBookings, ...result.data]
          );
        }
        setLoading(false);
      } catch (error) {
        console.error("Error fetching bookings:", error);
        setLoading(false);
        setHasMore(false);
      } finally {
        setLoading(false);
      }
    },
    [playerData._id]
  );

  const getUserToken = useCallback(
    async (page, reset = false) => {
      setLoading(true);
      let requestOptions = {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        redirect: "follow",
      };
      const response = await fetch("/api/profile", requestOptions);
      const result = await response.json();
      if (result?.user?.token) {
        fetchBookings(result.user.token, page, reset);
      }
    },
    [fetchBookings]
  );

  useEffect(() => {
    if (playerData?._id) {
      getUserToken(1, true);
      setPageNumber(1);
    }
  }, [playerData, getUserToken]);

  useEffect(() => {
    if (playerData?._id && pageNumber > 1) {
      getUserToken(pageNumber);
    }
  }, [pageNumber, getUserToken, playerData]);

  const fetchMoreData = () => {
    setPageNumber((prevPageNumber) => prevPageNumber + 1);
  };
  const ratingChanged = async (newRating, bookingId) => {
    let requestOptions = {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      redirect: "follow",
    };
    const response = await fetch("/api/profile", requestOptions);
    const result1 = await response.json();
    if (result1?.user?.token) {
      const requestOptions = {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${result1?.user?.token}`,
        },
        body: JSON.stringify({ stars: newRating }),
      };

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_BASE_URL}/api/course/ratings/${bookingId}`,
        requestOptions
      );
      const result = await response.json();
    }
  };
  const loader = (
    <div
      role="status"
      className="flex justify-center items-center overflow-y-auto mt-7"
    >
      <svg
        aria-hidden="true"
        className="w-10 h-10 text-gray-200 animate-spin dark:text-gray-200 fill-blue-500"
        viewBox="0 0 100 101"
        fill="none"
        xmlns="http://www.w3.org/2000/svg "
      >
        <path
          d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
          fill="currentColor"
        />
        <path
          d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
          fill="currentFill"
        />
      </svg>
      <span className="sr-only">Loading...</span>
    </div>
  );
  return (
    <>
      {loading && pageNumber === 1 ? (
        <div className="text-center">
          <div role="status" className="flex justify-center items-center">
            <svg
              aria-hidden="true"
              className="w-10 h-10 text-gray-200 animate-spin dark:text-gray-200 fill-blue-500"
              viewBox="0 0 100 101"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                fill="currentColor"
              />
              <path
                d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                fill="currentFill"
              />
            </svg>
            <span className="sr-only">Loading...</span>
          </div>
        </div>
      ) : (
        {filteredBookings?.length === 0 && bookings?.length > 0 ? (
          <div className="text-center py-8">
            <p className="text-gray-500">No bookings match the selected filters.</p>
          </div>
        ) : (
          <InfiniteScroll
            dataLength={filteredBookings.length}
            next={fetchMoreData}
            hasMore={hasMore}
            loader={loader}
            endMessage={<p>No more bookings to show</p>}
          >
            {filteredBookings?.length > 0 &&
              filteredBookings.map((booking) => {
              return (
                <div
                  key={booking._id}
                  className="md:border-hidden border bg-transparent rounded-lg relative flex justify-between gap-2 md:gap-1 flex-col md:flex-row"
                >
                  {booking?.courseId?.images[0]?.url && (
                    <Link
                      href={`/bookings/${booking._id}`}
                      className="w-full md:w-[15%]"
                    >
                      <Image
                        src={booking?.courseId?.images[0]?.url}
                        alt="CourseImage"
                        width={100}
                        height={100}
                        className="h-[10rem] w-full flex-none rounded-lg object-cover"
                      />
                    </Link>
                  )}
                  <div className="pb-4 px-4 w-full md:w-[60%] flex flex-col gap-2">
                    <div className="flex items-center justify-between">
                      <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-4">
                        <h3 className="font-semibold text-gray-900 text-base md:text-xl ">
                          {booking?.courseId?.courseName}
                        </h3>
                        {booking?.status === "Inactive" &&
                          (booking.rating ? (
                            <ReactStars
                              count={5}
                              // onChange={(newRating) => ratingChanged(newRating, booking._id)}
                              size={25}
                              value={booking?.stars}
                              color="#E5E4E2"
                              activeColor="#FCAB00"
                              edit={false}
                            />
                          ) : (
                            <ReactStars
                              count={5}
                              onChange={(newRating) =>
                                ratingChanged(newRating, booking._id)
                              }
                              size={25}
                              color="#E5E4E2"
                              activeColor="#FCAB00"
                            />
                          ))}
                      </div>
                    </div>
                    <div className="flex flex-col gap-[5px]">
                      {/* Price and Date Details */}
                      <p className="font-semibold text-sky-500 text-[16px]">
                        ₹{booking?.pricePaid}
                        <span className="text-[13px] text-gray-500">
                          {" "}
                          (inclusive all taxes)
                        </span>
                      </p>
                      <p className="font-semibold text-gray-500">
                        {booking.classes.length > 1
                          ? `${booking?.classes?.length} Classes Booked`
                          : `${booking?.classes?.length} Class Booked`}
                      </p>
                      {/* Venue Details */}
                      <div className="flex items-center gap-4">
                        <p className="font-semibold text-gray-500 flex items-center gap-2">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            strokeWidth="1.5"
                            stroke="currentColor"
                            aria-hidden="true"
                            className="w-[20px]"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              d="M15 10.5a3 3 0 11-6 0 3 3 0 016 0z"
                            ></path>
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              d="M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1115 0z"
                            ></path>
                          </svg>
                          {booking?.courseId?.facility?.addressLine1} ,{" "}
                          {booking?.courseId?.facility?.city}
                        </p>
                      </div>
                    </div>
                    {/* Session and Status Details */}
                    <div className="flex flex-row gap-6">
                      <div
                        className={`border-[#FCAB00] border text-[#FCAB00] border-solid py-[2px] px-2 rounded-md capitalize`}
                      >
                        {booking?.courseId?.classType === "class"
                          ? "Session"
                          : booking?.courseId?.classType}
                      </div>
                      <div
                        className={`border border-solid py-[2px] px-2 rounded-md capitalize ${
                          booking?.status === "Active"
                            ? "text-[#4CAF50] border-[#4CAF50]"
                            : booking?.status === "Inactive"
                            ? "text-[#FF0000] border-[#FF0000]"
                            : "text-[#4CAF50] border-[#4CAF50]"
                        }`}
                      >
                        {booking?.status === "Active"
                          ? "Active"
                          : booking?.status === "Inactive"
                          ? "InActive"
                          : booking?.status}
                      </div>
                    </div>
                  </div>
                  {booking?.groupSize > 1 ? (
                    <div className="text-center md:flex flex-col justify-around w-[50%] md:w-[15%] pl-4 pb-4 md:pl-0 md:pb-0">
                      <button
                        disabled
                        className="tooltip bg-red-300 border text-white border-solid py-1 px-3 rounded-md capitalize"
                      >
                        <span className="tooltiptext">
                          You can only cancel individual sessions
                        </span>
                        <p>Not Available</p>
                      </button>
                    </div>
                  ) : (
                    <div className="text-center md:flex flex-col justify-around md:w-[15%] w-[50%] pb-4 pl-4 md:pb-0 md:pl-0 ">
                      <CancellationModal booking={booking} />
                    </div>
                  )}
                </div>
              );
            })}
          </InfiniteScroll>
        )}
      )}
    </>
  );
}
