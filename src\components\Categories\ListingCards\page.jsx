
import Link from "next/link";
import Image from "next/image";
import { MapPinIcon } from "@heroicons/react/24/outline";
import axios from "axios";
import { useState, useEffect } from "react";
import { useParams, usePathname } from "next/navigation";
import InfiniteScroll from "react-infinite-scroll-component";
import ReactStars from "react-rating-stars-component";
const emailRegExp = /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i;
export default function ListingCards({ filterSearchUrl }) {
  const params = useParams();
  const pathname = usePathname();
  const [Courses, setCourses] = useState([]);
  const [hasMore, setHasMore] = useState(true);
  const [pageNumber, setPageNumber] = useState(1)
  const [courseArray, setCoursesArray] = useState([]);
  const [filterChange, setFilterChange] = useState(filterSearchUrl);
  const [filterBoolean, setFilterBoolean] = useState(false)
  const [playerEmail, setPlayerEmail] = useState('')
  const [email, setEmail] = useState("");
  const [emailError, setEmailError] = useState("");
  const [notifySuccess, setNotifySuccess] = useState(false);
  const [playerData, setPlayerData] = useState("");
  // let NewFilterSearchUrl = filterSearchUrl?.split("page=1&")[1];
  let pageNumberState = 1;
  let pageBoolean = false;
  if (typeof window !== "undefined") localStorage.setItem("filter", filterSearchUrl);

  const fetchPlayerData = async () => {
    try {
      const response = await axios.post("/api/profile");
      const result = response.data;

      if (result.user) {
        const { token, player_id } = result.user;
        const playerResponse = await axios.get(
          `${process.env.NEXT_PUBLIC_BASE_URL}/api/player/${player_id}`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        );
        const playerResult = playerResponse.data;
        setPlayerData(playerResult);
        setEmail(playerResult?.email || "")
      }
    } catch (error) {
      console.error("Error fetching player data:", error);
    }
  };

  useEffect(() => {
    fetchPlayerData();
  }, []);
  const fetchData = async () => {
    try {
      if (pageBoolean) return;
      if (pageNumberState === 1) pageBoolean = true;
      const response = await axios.get(
        `${process.env.NEXT_PUBLIC_BASE_URL}/api/course/filter?page=${pageNumber}&${filterSearchUrl}`
      );

      // Check if response.data is an array, if not, handle accordingly
      const courseData = Array.isArray(response.data.coursesAndCoaches) ? response.data.coursesAndCoaches : [];
      
      if (courseData.length === 0) {
        setHasMore(false);
      }
      if (filterBoolean) {
        setCourses(courseData);
      } else {
        setCourses((prevCourses) => [...prevCourses, ...courseData]);
      }
      setFilterBoolean(false);
    } catch (error) {
      console.error("Error fetching data:", error);
      setHasMore(false);
    }
  };

  useEffect(() => {
    if (filterSearchUrl != filterChange) {
      pageNumberState = 1;
      setPageNumber(1)
      pageBoolean = true;
      setFilterBoolean(true);
      setFilterChange(filterSearchUrl);
      setHasMore(true);
      setNotifySuccess(false);
    }
    fetchData();
  }, [filterSearchUrl, pageNumber, filterChange, hasMore]);

  const handleLoadMore = () => {
    setPageNumber((prevPage) => prevPage + 1);
  };

  useEffect(() => {
    const newCoursesArray = Courses.map((course, index) => ({
      "@type": "ListItem",
      position: index,
      item: {
        "@id": `${process.env.NEXT_PUBLIC_WEB_URL}/courses/${course._id}`,
        url: `${process.env.NEXT_PUBLIC_WEB_URL}/courses/${course._id}`,
      },
    }));
    setCoursesArray(newCoursesArray);
  }, [Courses]);

  const jsonLd = {
    "@context": "http://schema.org",
    "@type": "CollectionPage",
    "@id": `${process.env.NEXT_PUBLIC_WEB_URL}${pathname}`,
    name: params.slug,
    url: `${process.env.NEXT_PUBLIC_WEB_URL}${pathname}`,
    description: process.env.NEXT_PUBLIC_COLLECTION_DESCRIPTION,
    mainEntity: {
      "@type": "ItemList",
      itemListElement: courseArray,
    },
  };
  const handleEmailChange = (e) => {
    setEmail(e.target.value);
  };

  const handleNotifyMe = async () => {
    if (!emailRegExp.test(email)) {
      setEmailError("Please enter a valid email address.");
      return;
    }
    setEmailError("");
    try {
      const response = await axios.post(`${process.env.NEXT_PUBLIC_BASE_URL}/api/courseNotify`, {
        email,
        message: filterSearchUrl,
      });
      if (response.status === 200) {
        setNotifySuccess(true);
      }
    } catch (error) {
      console.error("Error sending notification request:", error);
    }
  };
  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
      <div className="bg-white">
        <div className="mx-auto max-w-2xl px-2 sm:px-6 sm:py-0 lg:max-w-7xl lg:px-8">
          <InfiniteScroll
            dataLength={Courses.length}
            next={handleLoadMore}
            hasMore={hasMore}
            loader={<h4>Loading...</h4>}
            endMessage={Courses.length > 0 ? <p>{"You've reached the end of the available courses."}</p> : ""}
            className="grid grid-cols-1 gap-y-4"
          >
            {Courses.length > 0
              ? Courses.map((course, index) => (
                <div
                  key={index}
                  className="md:gap-6 md:p-[12px] group relative flex flex-wrap sm:flex-nowrap overflow-hidden rounded-lg border border-gray-200 bg-white sm:flex-row lg:h-50"
                >
                  <div className="bg-inherit pl-4 pt-4 md:pl-0 md:pt-0 order-1 w-1/2 aspect-h-4 aspect-w-3 bg-gray-200 sm:aspect-none group-hover:opacity-75 sm:w-2/6">
                    {course.images && (
                      // <Link href={`/courses/${course.courseName}`}>
                      <Link
                        href={`/courses/${course._id}`}
                        className="relative"
                      >
                        <Image
                          priority
                          src={course?.images[0]?.url}
                          height={500}
                          width={500}
                          alt={course.courseName}
                          className="md:rounded-0 rounded-lg h-[180px] md:h-[250px] w-full object-cover object-center sm:w-full"
                        />
                        {course.classType === "class" ||
                          (course.camp && course.campName != "") ? (
                          <div className="absolute top-0 left-0 bg-sky-400 p-1 rounded-lg text-white text-sm">
                            {course.classType === "class"
                              ? "Individual Session"
                              : course.camp
                                ? course.campName
                                : ""}
                          </div>
                        ) : null}
                      </Link>
                    )}
                  </div>
                  <div className="md:border-t-0 border-t-[1px] border-gray-200 m-4 md:m-0 md:p-0 md:pt-0 pt-4 order-3 w-full sm:order-2 sm:w-2/4 flex flex-col space-y-2 sm:border-r-2 gap-2">
                    <h3>
                      {/* <Link className="flex gap-1 text-base" href={`/courses/${course.courseName}`}> */}
                      <Link
                        className="flex gap-1 text-base"
                        href={`/courses/${course._id}`}
                      >
                        {/* <span aria-hidden="true" className="absolute inset-0" /> */}
                        <p>{course.courseName}</p>
                        <p className="text-gray-500">
                          ({course.classType == "class" ? "Class" : "Course"})
                        </p>
                      </Link>
                    </h3>

                    <div className="text-sm text-black flex gap-6">
                      <p>Details:</p>
                      <div
                        className="text-gray-500 line-clamp-2"
                        dangerouslySetInnerHTML={{
                          __html: course.description,
                        }}
                      />
                    </div>
                    <div className="flex gap-2 items-center text-sm">
                      <p>Proficiency:</p>
                      <div className="grid grid-cols-2 gap-2 w-9/12 sm:w-8/12">
                        {course && course?.proficiency?.map((proficiency, index) => (
                          <p
                            key={index}
                            className="bg-sky-100 px-2 rounded-md border-x border-y border-sky-500 text-sky-500"
                          >
                            {proficiency.toUpperCase()}
                          </p>
                        ))}
                      </div>
                    </div>

                    <div className="flex gap-6 items-center text-sm">
                      <p>Facility:</p>
                      <div className="flex justify-between w-7/12 sm:w-8/12">
                        <p className="text-gray-500">
                          {course.facility?.name}
                        </p>
                      </div>
                    </div>
                    <div className="w-11/12 flex items-start justify-between">
                      <div className="mt-3 w-4/5 items-start text-sm flex gap-1">
                        <MapPinIcon style={{ width: '1.25rem', height: '1.25rem', flexShrink: 0 }} aria-hidden="true" />
                        <div className="text-gray-500">
                          <p>{`${course?.facility?.addressLine1}, ${course?.facility?.addressLine2}`}</p>
                          <p className="line-clamp-1">{`${course?.facility?.city}, ${course?.facility?.state}`}</p>
                        </div>
                      </div>
                      {course?.facility?.location?.coordinates[0] && course?.facility?.location?.coordinates[1] && (
                        <Link
                          href={`https://www.google.com/maps/search/?api=1&query=${course?.facility?.location?.coordinates[0]},${course?.facility?.location?.coordinates[1]}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="mt-3 w-1/3 lg:w-1/4 text-sm underline text-sky-500 whitespace-nowrap"
                        >
                          Show on map
                        </Link>
                      )}
                    </div>

                  </div>
                  <div className="md:m-0 order-2 w-1/2 pb-0 p-4 sm:p-0 sm:order-3 sm:w-1/5 sm:m-3 font-normal not-italic text-sm flex flex-col gap-1">
                    {/* <Image
                      src="/review.svg"
                      alt="arrowsvg"
                      width={20}
                      height={20}
                      style={{ width: "70%", height: "auto" }}
                    /> */}
                    <ReactStars
                      count={5}
                      value={course?.ratings?.stars}
                      edit={false}
                      size={18}
                      color="#E5E4E2"
                      activeColor="#FCAB00"
                    />
                    <p className="text-slate-400">{`${course?.ratings ? course?.ratings?.noOfRatings : 0
                      } Reviews`}</p>
                    <p className="mt-2 text-black">{`Group Size: ${course?.maxGroupSize}`}</p>
                    {course?.fees?.feesCourse ? (
                      <p className="mt-4 text-base text-white px-3.5 py-2 bg-red-600 rounded-lg w-fit">
                        {`₹ ${Math.round(course?.fees?.feesCourse)}`}
                      </p>
                    ) : (
                      <Link
                        className="mt-4 text-base text-white px-3.5 py-2 bg-red-600 rounded-lg w-fit"
                        href={`/courses/${course?._id}`}
                      >
                        Book Now
                      </Link>
                    )}
                    <div className="mt-5 flex items-center underline gap-2 text-base">
                      <Link href={`/courses/${course?._id}`}>View More</Link>
                      <Image
                        src="/Arrow.svg"
                        alt="arrowsvg"
                        width={20}
                        height={20}
                        className="w-3 h-auto"
                      />
                    </div>
                  </div>
                </div>
              ))
              : (
                <div className="flex flex-col">
                  <p>No courses available at the moment , Enter your email to get notified when courses are available.</p>
                  {notifySuccess ? (
                    <p className="mt-4 text-green-500">You will be notified when new courses are available.</p>
                  ) : (
                    <form
                      onSubmit={(e) => {
                        e.preventDefault();
                        handleNotifyMe();
                      }}
                      className="mt-4 w-full max-w-sm"
                    >
                      <div className="flex items-center border-b border-sky-500 py-2">
                        <input
                          className="appearance-none bg-transparent border-none w-full text-gray-700 mr-3 py-1 px-2 leading-tight focus:outline-none"
                          type="email"
                          placeholder="Enter your email"
                          aria-label="Email"
                          value={email}
                          onChange={handleEmailChange}
                        />
                        <button
                          className="flex-shrink-0 bg-sky-500 hover:bg-sky-700 border-sky-500 hover:border-sky-700 text-sm border-4 text-white py-1 px-2 rounded"
                          type="submit"
                        >
                          Notify Me
                        </button>
                      </div>
                      {emailError && <p className="text-red-500 text-sm mt-2">{emailError}</p>}
                    </form>
                  )}
                </div>
              )}
          </InfiniteScroll>
        </div>
      </div>
    </>
  );
}
