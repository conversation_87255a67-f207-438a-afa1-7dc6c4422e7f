import Link from "next/link";
import { useState, useMemo, useEffect } from "react";
import { useRouter } from "next/navigation";
import OTPModal from "../../../OTPModal/page";

export default function CancellationModal({ booking }) {
  const router = useRouter();
  const [selectedDates, setSelectedDates] = useState([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedRadioOption, setSelectedRadioOption] = useState(null);
  const [isOverflowHidden, setIsOverflowHidden] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isOTPModalOpen, setIsOTPModalOpen] = useState(false);
  const [attendanceMarked, setAttendanceMarked] = useState(false);

  // Function to check if attendance is already marked for current date's class
  const checkAttendanceStatus = async () => {
    const currentClassId = getCurrentDateClassId;
    if (!currentClassId || !booking?._id) return;

    // Create a unique key for this specific class attendance
    const attendanceKey = `attendance_${booking._id}_${currentClassId}`;

    // Check localStorage for attendance status first
    const storedAttendance = localStorage.getItem(attendanceKey);
    console.log('Checking attendance for key:', attendanceKey, 'Stored value:', storedAttendance);
    if (storedAttendance === 'true') {
      console.log('Attendance found in localStorage, marking as attended');
      setAttendanceMarked(true);
      return;
    }

    // Check if the class status indicates attendance was marked
    const currentClass = booking.classes.find(classItem => classItem._id === currentClassId);
    if (currentClass && (currentClass.attendanceMarked || currentClass.status === 'completed' || currentClass.status === 'attended')) {
      setAttendanceMarked(true);
      localStorage.setItem(attendanceKey, 'true');
      return;
    }

    // As a fallback, try to fetch fresh booking data to check attendance status
    try {
      const response = await fetch('/api/profile', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        redirect: 'follow',
      });

      const result = await response.json();
      if (result?.user?.token) {
        const requestOptions = {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${result.user.token}`,
          },
          redirect: 'follow',
        };

        // Fetch fresh booking data
        const bookingResponse = await fetch(
          `${process.env.NEXT_PUBLIC_BASE_URL}/api/booking/${booking._id}`,
          requestOptions
        );

        if (bookingResponse.ok) {
          const freshBookingData = await bookingResponse.json();
          const freshCurrentClass = freshBookingData.classes?.find(classItem => classItem._id === currentClassId);

          if (freshCurrentClass && (freshCurrentClass.attendanceMarked || freshCurrentClass.status === 'completed' || freshCurrentClass.status === 'attended')) {
            setAttendanceMarked(true);
            localStorage.setItem(attendanceKey, 'true');
          }
        }
      }
    } catch (error) {
      console.error('Error checking attendance status from API:', error);
    }
  };

  // Check attendance status when component mounts or when current class changes
  useEffect(() => {
    checkAttendanceStatus();
  }, [getCurrentDateClassId, booking?._id]);

  // Function to clear attendance status (for testing/debugging)
  const clearAttendanceStatus = () => {
    const currentClassId = getCurrentDateClassId;
    if (currentClassId && booking?._id) {
      const attendanceKey = `attendance_${booking._id}_${currentClassId}`;
      localStorage.removeItem(attendanceKey);
      setAttendanceMarked(false);
      console.log('Attendance status cleared for testing');
    }
  };

  // Utility function to check if mark attendance should be enabled
  const isMarkAttendanceEnabled = useMemo(() => {
    if (!booking?.classes || booking.classes.length === 0) return false;

    const now = new Date();
    const currentDate = now.toISOString().split('T')[0]; // YYYY-MM-DD format

    // Find today's class
    const todaysClass = booking.classes.find(classItem => {
      const classDate = new Date(classItem.date).toISOString().split('T')[0];
      return classDate === currentDate && classItem.status === 'upcoming';
    });

    if (!todaysClass) return false;

    // Parse the scheduled time
    const [hours, minutes] = todaysClass.startTime.split(':').map(Number);
    const scheduledTime = new Date();
    scheduledTime.setHours(hours, minutes, 0, 0);

    // Calculate the allowed time window (15 minutes before to 15 minutes after)
    const windowStart = new Date(scheduledTime.getTime() - 15 * 60 * 1000); // 15 minutes before
    const windowEnd = new Date(scheduledTime.getTime() + 15 * 60 * 1000); // 15 minutes after

    return now >= windowStart && now <= windowEnd;
  }, [booking]);

  // Get the current date's class ID for attendance marking
  const getCurrentDateClassId = useMemo(() => {
    if (!booking?.classes || booking.classes.length === 0) return null;

    const now = new Date();
    const currentDate = now.toISOString().split('T')[0]; // YYYY-MM-DD format

    // Find today's class
    const todaysClass = booking.classes.find(classItem => {
      const classDate = new Date(classItem.date).toISOString().split('T')[0];
      return classDate === currentDate && classItem.status === 'upcoming';
    });

    return todaysClass?._id || null;
  }, [booking]);

  // Utility function to check if cancel/reschedule should be enabled
  const isCancelRescheduleEnabled = useMemo(() => {
    if (!booking?.classes || booking.classes.length === 0) return false;

    // If mark attendance is enabled, cancel/reschedule should be disabled
    if (isMarkAttendanceEnabled) return false;

    const now = new Date();

    // Check if ANY upcoming class is within 1 hour - if so, disable cancel/reschedule
    const hasClassWithinOneHour = booking.classes.some(classItem => {
      if (classItem.status !== 'upcoming') return false;

      const classDateTime = new Date(classItem.date);
      const [hours, minutes] = classItem.startTime.split(':').map(Number);
      classDateTime.setHours(hours, minutes, 0, 0);

      // Check if class is within 1 hour
      const oneHourFromNow = new Date(now.getTime() + 60 * 60 * 1000);
      return classDateTime <= oneHourFromNow;
    });

    // If any class is within 1 hour, disable cancel/reschedule
    return !hasClassWithinOneHour;
  }, [booking, isMarkAttendanceEnabled]);

  const handleOTPClick = () => {
    // Only open OTP modal if we have a valid class ID for today
    if (getCurrentDateClassId) {
      console.log('Opening OTP modal for class ID:', getCurrentDateClassId);
      console.log('Current attendance status:', attendanceMarked);
      setIsOTPModalOpen(true);
    } else {
      console.error('No valid class found for current date');
      console.log('Available classes:', booking?.classes);
    }
  };
  const handleOTPSuccess = () => {
    const currentClassId = getCurrentDateClassId;
    if (currentClassId && booking?._id) {
      // Store attendance status in localStorage
      const attendanceKey = `attendance_${booking._id}_${currentClassId}`;
      localStorage.setItem(attendanceKey, 'true');
      console.log('Attendance marked and stored in localStorage with key:', attendanceKey);
    }

    setAttendanceMarked(true);
    setIsOTPModalOpen(false);
    console.log('Attendance successfully marked, button should now be green');
  };

  const handleOptionChange = (e) => {
    setSelectedRadioOption(e.target.value);
  };

  const toggleDateSelection = (date) => {
    if (selectedDates.includes(date)) {
      setSelectedDates(selectedDates.filter((d) => d !== date));
    } else {
      setSelectedDates([...selectedDates, date]);
    }
  };

  const openModal = () => {
    setIsModalOpen(true);
    setIsOverflowHidden(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setIsOverflowHidden(false);
  };
  const handleCancellation = async () => {
    try {
      setIsLoading(true);
      let requestOptions = {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        redirect: "follow",
      };
      const response = await fetch("/api/profile", requestOptions);
      const result = await response.json();
      if (result?.user?.token) {
        const dates = booking.classes.filter((x) =>
          selectedDates.includes(
            `${new Date(x?.date).toLocaleDateString("en-IN", {
              day: "numeric",
              month: "long",
              year: "numeric",
            })} (${x?.startTime} - ${x?.endTime})`
          )
        );
        console.log([...booking.classes], selectedDates, "all dates");
        const myHeaders = new Headers();
        myHeaders.append("Content-Type", "application/json");
        myHeaders.append("Authorization", `Bearer ${result?.user?.token}`);
        const raw = JSON.stringify({
          sender: "player",
          status:
            selectedRadioOption === "cancel" ? "cancelled" : "rescheduled",
          classes: dates,
        });
        const requestOptions = {
          method: "POST",
          headers: myHeaders,
          body: raw,
          redirect: "follow",
        };
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_BASE_URL}/api/booking/cancel/${booking?._id}`,
          requestOptions
        );
        const result1 = await response.json();

        if (result1) {
          setIsLoading(false);
          setIsModalOpen(false);
          setSelectedDates([]);
          setSelectedRadioOption(null);
          selectedRadioOption == "reschedule"
            ? router.push(`/courses/${booking?.courseId?._id}`)
            : alert("Booking Cancelled Successfully");
          if (selectedRadioOption != "reschedule") window.location.reload();
        } else {
          alert("Failed to cancel booking");
        }
      }
    } catch (error) {
      console.error("Error cancelling booking:", error);
      alert("An error occurred while cancelling booking Please try again");
    }
  };
  return (
    <div>
      {booking?.status == "Inactive" ? (
        <>
          <div className="bg-red-300 border text-white border-solid py-1 px-3 rounded-md capitalize cursor-default">
            <p>InActive</p>
          </div>
          {/* {booking.classes[0]?.status == "rescheduled" && (<div className="bg-red-300 border text-white border-solid py-1 px-3 rounded-md capitalize cursor-default">
                        <p>Rescheduled</p>
                    </div>)} */}
        </>
      ) : (
        <>
          <div
            onClick={!attendanceMarked && isMarkAttendanceEnabled ? handleOTPClick : undefined}
            className={`border text-white border-solid py-1 px-3 rounded-md capitalize mb-2 ${
              attendanceMarked
                ? "bg-green-500 cursor-default"
                : isMarkAttendanceEnabled
                ? "bg-red-500 hover:bg-red-600 cursor-pointer"
                : "bg-gray-400 cursor-not-allowed"
            }`}
            style={
              attendanceMarked || !isMarkAttendanceEnabled ? { pointerEvents: "none", opacity: 0.7 } : {}
            }
          >
            <p>
              {attendanceMarked
                ? "Attendance Marked"
                : isMarkAttendanceEnabled
                ? "Mark Attendance"
                : "Mark Attendance"
              }
            </p>
          </div>
          <div
            onClick={isCancelRescheduleEnabled ? openModal : undefined}
            className={`border text-white border-solid py-1 px-3 rounded-md capitalize ${
              isCancelRescheduleEnabled
                ? "bg-red-500 hover:bg-red-600 cursor-pointer"
                : "bg-gray-400 cursor-not-allowed"
            }`}
            style={
              !isCancelRescheduleEnabled ? { pointerEvents: "none", opacity: 0.7 } : {}
            }
          >
            <p>
              {isCancelRescheduleEnabled
                ? "Cancel/Reschedule"
                : "Cancel/Reschedule"
              }
            </p>
          </div>
        </>
      )}

      <OTPModal
        isOpen={isOTPModalOpen}
        onClose={() => setIsOTPModalOpen(false)}
        booking={booking}
        classId={getCurrentDateClassId} // Send the current date's class ID
        playerId={booking?.player}
        courseId={booking?.courseId?._id}
        onSuccess={handleOTPSuccess}
      />

      {isModalOpen && (
        <div
          className={`fixed top-0 left-0 w-full h-full flex items-center justify-center bg-gray-500 bg-opacity-50 z-50 ${
            isOverflowHidden ? "overflow-hidden" : ""
          }`}
        >
          <div className="md:w-[50%] w-[95%] bg-white p-6 rounded-lg shadow-lg flex flex-col gap-4 max-h-[400px] lg:max-h-[500px] overflow-y-auto">
            <div>Cancellation & Refund</div>
            <div className="flex items-center justify-around">
              <div className=" flex items-center gap-2">
                <input
                  type="radio"
                  id="cancel"
                  name="cancelReschedule"
                  value="cancel"
                  checked={selectedRadioOption === "cancel"}
                  onChange={handleOptionChange}
                />
                <label htmlFor="cancel">Cancel</label>
              </div>
              <div className=" flex items-center gap-2">
                <input
                  type="radio"
                  id="reschedule"
                  name="cancelReschedule"
                  value="reschedule"
                  checked={selectedRadioOption === "reschedule"}
                  onChange={handleOptionChange}
                />
                <label htmlFor="reschedule">Reschedule</label>
              </div>
            </div>
            <div>
              {booking?.classes?.map((bookingTime, index) => {
                const date = new Date(bookingTime?.date).toLocaleDateString(
                  "en-IN",
                  { day: "numeric", month: "long", year: "numeric" }
                );
                return (
                  <div key={index} className="flex items-center gap-4">
                    <input
                      disabled={bookingTime?.status != "upcoming"}
                      type="checkbox"
                      id={`date-${index}`}
                      checked={selectedDates.includes(
                        `${date} (${bookingTime?.startTime} - ${bookingTime?.endTime})`
                      )}
                      onChange={() =>
                        toggleDateSelection(
                          `${date} (${bookingTime?.startTime} - ${bookingTime?.endTime})`
                        )
                      }
                    />
                    <label
                      htmlFor={`date-${index}`}
                      className={`flex items-center gap-2 font-semibold  ${
                        bookingTime?.status != "upcoming"
                          ? "text-gray-300"
                          : "text-gray-600"
                      }`}
                    >
                      {`${date} (${bookingTime?.startTime} - ${bookingTime?.endTime})`}
                      <p
                        className={`uppercase ${
                          bookingTime?.status == "upcoming"
                            ? "text-green-500"
                            : "text-red-500"
                        } `}
                      >
                        {bookingTime?.status}
                      </p>
                    </label>
                  </div>
                );
              })}
            </div>
            <div className="flex justify-end gap-6">
              <button
                className={`text-white h-[32px] w-[67px] px-2 py-1 rounded-md ${
                  selectedRadioOption && selectedDates.length > 0
                    ? "bg-black"
                    : "bg-gray-500"
                } ${isLoading ? "relative min-w-[70px]" : ""} ${
                  selectedRadioOption === "reschedule" ? "w-[96px]" : "w-[67px]"
                }`}
                onClick={handleCancellation}
                disabled={
                  !selectedRadioOption ||
                  selectedDates.length === 0 ||
                  isLoading
                }
              >
                {isLoading && (
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="w-5 h-5 border-t-2 border-b-2 border-white-900 rounded-full animate-spin "></div>
                  </div>
                )}
                <p className={`${isLoading ? "hidden" : "block"} `}>
                  {selectedRadioOption === "reschedule"
                    ? "Reschedule"
                    : "Confirm"}
                </p>
              </button>

              <button onClick={closeModal}>Cancel</button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
